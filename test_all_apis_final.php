<?php

/**
 * Test final complet de toutes les APIs ClockIn
 * Ce script teste tous les endpoints avec différents scénarios
 */

echo "🚀 TESTS FINAUX API CLOCKIN\n";
echo "===========================\n\n";

// Configuration
$baseUrl = 'http://localhost:8001/api';
$results = [];

/**
 * Fonction pour faire des requêtes HTTP
 */
function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    $defaultHeaders = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $allHeaders = array_merge($defaultHeaders, $headers);
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $allHeaders,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return [
            'success' => false,
            'error' => $error,
            'http_code' => 0
        ];
    }
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'data' => json_decode($response, true),
        'raw_response' => $response
    ];
}

/**
 * Enregistrer un résultat de test
 */
function recordTest($testName, $success, $details = '') {
    global $results;
    
    $status = $success ? '✅' : '❌';
    echo "{$status} {$testName}";
    if ($details) {
        echo " - {$details}";
    }
    echo "\n";
    
    $results[] = [
        'name' => $testName,
        'success' => $success,
        'details' => $details
    ];
    
    return $success;
}

/**
 * Tests d'authentification
 */
function testAuthentication() {
    global $baseUrl;
    
    echo "\n🔐 TESTS D'AUTHENTIFICATION\n";
    echo "============================\n";
    
    // Test login avec des identifiants valides
    $loginData = [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ];
    
    $response = makeRequest($baseUrl . '/login', 'POST', $loginData);
    
    if ($response['success'] && isset($response['data']['data']['token'])) {
        $token = $response['data']['data']['token'];
        recordTest('Login employé valide', true, 'Token généré');
        
        // Test du profil utilisateur
        $headers = ["Authorization: Bearer {$token}"];
        $response = makeRequest($baseUrl . '/me', 'GET', null, $headers);
        recordTest('Récupération profil utilisateur', $response['success']);
        
        // Test logout
        $response = makeRequest($baseUrl . '/logout', 'POST', null, $headers);
        recordTest('Logout', $response['success']);
        
        return $token;
    } else {
        recordTest('Login employé valide', false, 'Échec de connexion');
        return false;
    }
}

/**
 * Tests des endpoints de pointage
 */
function testPointageEndpoints($token) {
    global $baseUrl;
    
    echo "\n⏰ TESTS ENDPOINTS POINTAGE\n";
    echo "===========================\n";
    
    if (!$token) {
        recordTest('Tests pointage', false, 'Token requis');
        return false;
    }
    
    $headers = ["Authorization: Bearer {$token}"];
    
    // Test vérification de localisation
    $locationData = [
        'latitude' => 33.5731,
        'longitude' => -7.5898,
        'site_id' => 1
    ];
    
    $response = makeRequest($baseUrl . '/check-location', 'POST', $locationData, $headers);
    recordTest('Vérification localisation', $response['success']);
    
    // Test sauvegarde pointage
    $pointageData = [
        'site_id' => 1,
        'type' => 'entree',
        'latitude' => 33.5731,
        'longitude' => -7.5898
    ];
    
    $response = makeRequest($baseUrl . '/save-pointage', 'POST', $pointageData, $headers);
    recordTest('Sauvegarde pointage entrée', $response['success']);
    
    // Test sauvegarde pointage sortie
    $pointageData['type'] = 'sortie';
    $response = makeRequest($baseUrl . '/save-pointage', 'POST', $pointageData, $headers);
    recordTest('Sauvegarde pointage sortie', $response['success']);
    
    return true;
}

/**
 * Tests des endpoints de sites
 */
function testSiteEndpoints($token) {
    global $baseUrl;
    
    echo "\n🏢 TESTS ENDPOINTS SITES\n";
    echo "========================\n";
    
    if (!$token) {
        recordTest('Tests sites', false, 'Token requis');
        return false;
    }
    
    $headers = ["Authorization: Bearer {$token}"];
    
    // Test récupération des sites de l'utilisateur
    $response = makeRequest($baseUrl . '/my-sites', 'GET', null, $headers);
    recordTest('Récupération mes sites', $response['success']);
    
    return true;
}

/**
 * Tests avec admin
 */
function testAdminEndpoints() {
    global $baseUrl;
    
    echo "\n👑 TESTS ENDPOINTS ADMIN\n";
    echo "========================\n";
    
    // Login admin
    $adminData = [
        'email' => '<EMAIL>',
        'password' => 'password123'
    ];
    
    $response = makeRequest($baseUrl . '/login', 'POST', $adminData);
    
    if ($response['success'] && isset($response['data']['data']['token'])) {
        $adminToken = $response['data']['data']['token'];
        recordTest('Login admin', true);
        
        $headers = ["Authorization: Bearer {$adminToken}"];
        
        // Test liste des employés
        $response = makeRequest($baseUrl . '/employees', 'GET', null, $headers);
        recordTest('Liste employés (admin)', $response['success']);
        
        // Test liste des sites
        $response = makeRequest($baseUrl . '/sites', 'GET', null, $headers);
        recordTest('Liste sites (admin)', $response['success']);
        
        // Test liste des pointages
        $response = makeRequest($baseUrl . '/pointages', 'GET', null, $headers);
        recordTest('Liste pointages (admin)', $response['success']);
        
        // Logout admin
        $response = makeRequest($baseUrl . '/logout', 'POST', null, $headers);
        recordTest('Logout admin', $response['success']);
        
        return true;
    } else {
        recordTest('Login admin', false);
        return false;
    }
}

/**
 * Tests d'erreurs
 */
function testErrorCases() {
    global $baseUrl;
    
    echo "\n❌ TESTS CAS D'ERREURS\n";
    echo "======================\n";
    
    // Test login avec identifiants invalides
    $invalidLogin = [
        'email' => '<EMAIL>',
        'password' => 'wrongpassword'
    ];
    
    $response = makeRequest($baseUrl . '/login', 'POST', $invalidLogin);
    recordTest('Login identifiants invalides', !$response['success'] || $response['http_code'] === 401, 'Doit échouer');
    
    // Test accès endpoint protégé sans token
    $response = makeRequest($baseUrl . '/me', 'GET');
    recordTest('Accès /me sans token', !$response['success'] || $response['http_code'] === 401, 'Doit échouer');
    
    // Test endpoint inexistant
    $response = makeRequest($baseUrl . '/nonexistent', 'GET');
    recordTest('Endpoint inexistant', $response['http_code'] === 404, 'Doit retourner 404');
    
    return true;
}

/**
 * Exécution de tous les tests
 */
function runAllTests() {
    echo "🔍 Démarrage des tests complets...\n";
    
    // 1. Tests d'authentification
    $employeeToken = testAuthentication();
    
    // 2. Tests des endpoints de pointage
    if ($employeeToken) {
        testPointageEndpoints($employeeToken);
        testSiteEndpoints($employeeToken);
    }
    
    // 3. Tests admin
    testAdminEndpoints();
    
    // 4. Tests d'erreurs
    testErrorCases();
    
    // Résumé final
    global $results;
    echo "\n" . str_repeat('=', 50) . "\n";
    echo "📊 RÉSUMÉ FINAL DES TESTS\n";
    echo str_repeat('=', 50) . "\n";
    
    $passed = 0;
    $total = count($results);
    
    foreach ($results as $result) {
        $status = $result['success'] ? '✅' : '❌';
        echo "{$status} {$result['name']}\n";
        if ($result['success']) $passed++;
    }
    
    echo "\nRésultat: {$passed}/{$total} tests réussis\n";
    
    if ($passed === $total) {
        echo "🎉 TOUS LES TESTS SONT PASSÉS AVEC SUCCÈS!\n";
        echo "✅ L'API ClockIn est entièrement fonctionnelle\n";
    } else {
        echo "⚠️  Certains tests ont échoué\n";
    }
    
    echo "\n💡 Pour utiliser l'API:\n";
    echo "1. Serveur: http://localhost:8001\n";
    echo "2. Login: POST /api/login\n";
    echo "3. Utiliser le token Bearer pour les endpoints protégés\n";
    echo "4. Documentation: http://localhost:8001/docs (si Scribe configuré)\n";
    
    return $passed === $total;
}

// Exécution des tests
if (php_sapi_name() === 'cli') {
    $success = runAllTests();
    exit($success ? 0 : 1);
}
