<?php

/**
 * Test complet du système de pointage
 */

echo "⏰ TEST COMPLET SYSTÈME POINTAGE\n";
echo "=================================\n\n";

$baseUrl = 'http://localhost:8001/api';

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    $defaultHeaders = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $allHeaders = array_merge($defaultHeaders, $headers);
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $allHeaders,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "📡 {$method} {$url}\n";
    if ($data) {
        echo "📤 Data: " . json_encode($data) . "\n";
    }
    echo "📊 HTTP Code: {$httpCode}\n";
    
    if ($error) {
        echo "❌ Error: {$error}\n";
        return ['success' => false, 'error' => $error, 'http_code' => 0];
    }
    
    $decodedResponse = json_decode($response, true);
    if ($decodedResponse) {
        echo "📥 Response: " . json_encode($decodedResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    echo str_repeat('-', 50) . "\n";
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'data' => $decodedResponse,
        'raw_response' => $response
    ];
}

// 1. Login employé
echo "🔐 LOGIN EMPLOYÉ\n";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$response = makeRequest($baseUrl . '/login', 'POST', $loginData);

if (!$response['success'] || !isset($response['data']['data']['token'])) {
    echo "❌ Impossible de se connecter\n";
    exit(1);
}

$token = $response['data']['data']['token'];
$headers = ["Authorization: Bearer {$token}"];

echo "✅ Token obtenu\n\n";

// 2. Test pointage d'entrée
echo "🚪 TEST POINTAGE ENTRÉE\n";
$pointageEntree = [
    'site_id' => 1,
    'type' => 'entree',
    'latitude' => 33.5731,
    'longitude' => -7.5898
];
$response = makeRequest($baseUrl . '/save-pointage', 'POST', $pointageEntree, $headers);

if ($response['success']) {
    echo "✅ Pointage d'entrée réussi\n\n";
    
    // 3. Test pointage d'entrée en double (doit échouer)
    echo "🚫 TEST POINTAGE ENTRÉE EN DOUBLE (doit échouer)\n";
    $response = makeRequest($baseUrl . '/save-pointage', 'POST', $pointageEntree, $headers);
    
    if (!$response['success'] && $response['http_code'] === 422) {
        echo "✅ Pointage en double correctement rejeté\n\n";
    } else {
        echo "❌ Pointage en double non rejeté\n\n";
    }
    
    // 4. Test pointage de sortie
    echo "🚪 TEST POINTAGE SORTIE\n";
    $pointageSortie = [
        'site_id' => 1,
        'type' => 'sortie',
        'latitude' => 33.5731,
        'longitude' => -7.5898
    ];
    $response = makeRequest($baseUrl . '/save-pointage', 'POST', $pointageSortie, $headers);
    
    if ($response['success']) {
        echo "✅ Pointage de sortie réussi\n\n";
        
        // 5. Test pointage de sortie sans entrée (doit échouer)
        echo "🚫 TEST POINTAGE SORTIE SANS ENTRÉE (doit échouer)\n";
        $response = makeRequest($baseUrl . '/save-pointage', 'POST', $pointageSortie, $headers);
        
        if (!$response['success'] && $response['http_code'] === 422) {
            echo "✅ Pointage de sortie sans entrée correctement rejeté\n\n";
        } else {
            echo "❌ Pointage de sortie sans entrée non rejeté\n\n";
        }
    } else {
        echo "❌ Pointage de sortie échoué\n\n";
    }
} else {
    echo "❌ Pointage d'entrée échoué\n\n";
}

// 6. Test avec un autre site
echo "🏢 TEST AVEC AUTRE SITE\n";
$pointageAutreSite = [
    'site_id' => 3,
    'type' => 'entree',
    'latitude' => 31.6295,
    'longitude' => -7.9811
];
$response = makeRequest($baseUrl . '/save-pointage', 'POST', $pointageAutreSite, $headers);

if ($response['success']) {
    echo "✅ Pointage sur autre site réussi\n\n";
} else {
    echo "❌ Pointage sur autre site échoué\n\n";
}

echo "✅ Tests de pointage terminés\n";
