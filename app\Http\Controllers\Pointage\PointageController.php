<?php

namespace App\Http\Controllers\Pointage;

use App\Http\Controllers\Controller;
use App\Http\Requests\CheckLocationRequest;
use App\Http\Requests\PointageRequest;
use App\Http\Resources\PointageResource;
use App\Models\Site;
use App\Models\Pointage;
use App\Models\Log;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PointageController extends Controller
{
    /**
     * Check if location is within site range
     */
    public function checkLocation(CheckLocationRequest $request): JsonResponse
    {
        $validated = $request->validated();
        
        $site = Site::findOrFail($validated['site_id']);
        
        $isWithinRange = $site->isWithinRange(
            $validated['latitude'],
            $validated['longitude']
        );
        
        // Log location check
        Log::createLog(
            auth()->id(),
            'location_check',
            $isWithinRange ? 'success' : 'failed',
            [
                'site_id' => $validated['site_id'],
                'latitude' => $validated['latitude'],
                'longitude' => $validated['longitude'],
                'is_within_range' => $isWithinRange
            ]
        );
        
        return response()->json([
            'success' => true,
            'data' => [
                'is_within_range' => $isWithinRange,
                'site' => [
                    'id' => $site->id,
                    'name' => $site->name,
                    'latitude' => (float) $site->latitude,
                    'longitude' => (float) $site->longitude
                ]
            ],
            'message' => [
                'en' => $isWithinRange ? 'Location is within range' : 'Location is outside range',
                'fr' => $isWithinRange ? 'Position dans la zone' : 'Position hors zone',
                'ar' => $isWithinRange ? 'الموقع ضمن النطاق' : 'الموقع خارج النطاق'
            ]
        ]);
    }
    
    /**
     * Save pointage
     */
    public function savePointage(PointageRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $userId = auth()->id();
        $now = now();

        try {
            // Vérifier s'il y a un pointage en cours pour cet utilisateur et ce site
            $existingPointage = Pointage::where('user_id', $userId)
                ->where('site_id', $validated['site_id'])
                ->whereNull('fin_pointage')
                ->first();

            if ($validated['type'] === 'entree') {
                // Vérifier qu'il n'y a pas déjà un pointage d'entrée en cours
                if ($existingPointage) {
                    return response()->json([
                        'success' => false,
                        'message' => [
                            'en' => 'You already have an active check-in for this site',
                            'fr' => 'Vous avez déjà un pointage d\'entrée actif pour ce site',
                            'ar' => 'لديك بالفعل تسجيل دخول نشط لهذا الموقع'
                        ]
                    ], 422);
                }

                // Créer un nouveau pointage d'entrée
                $pointage = Pointage::create([
                    'user_id' => $userId,
                    'site_id' => $validated['site_id'],
                    'debut_pointage' => $now,
                    'debut_latitude' => $validated['latitude'],
                    'debut_longitude' => $validated['longitude'],
                ]);

                $message = [
                    'en' => 'Check-in recorded successfully',
                    'fr' => 'Pointage d\'entrée enregistré avec succès',
                    'ar' => 'تم تسجيل الدخول بنجاح'
                ];

            } else { // sortie
                // Vérifier qu'il y a un pointage d'entrée en cours
                if (!$existingPointage) {
                    return response()->json([
                        'success' => false,
                        'message' => [
                            'en' => 'No active check-in found for this site',
                            'fr' => 'Aucun pointage d\'entrée actif trouvé pour ce site',
                            'ar' => 'لم يتم العثور على تسجيل دخول نشط لهذا الموقع'
                        ]
                    ], 422);
                }

                // Mettre à jour le pointage existant avec la sortie
                $existingPointage->update([
                    'fin_pointage' => $now,
                    'fin_latitude' => $validated['latitude'],
                    'fin_longitude' => $validated['longitude'],
                ]);

                $pointage = $existingPointage;

                $message = [
                    'en' => 'Check-out recorded successfully',
                    'fr' => 'Pointage de sortie enregistré avec succès',
                    'ar' => 'تم تسجيل الخروج بنجاح'
                ];
            }

            // Log successful pointage
            Log::createLog(
                $userId,
                'pointage_attempt',
                'success',
                [
                    'pointage_id' => $pointage->id,
                    'type' => $validated['type'],
                    'site_id' => $validated['site_id']
                ]
            );

            return response()->json([
                'success' => true,
                'message' => $message,
                'data' => new PointageResource($pointage->load(['user', 'site']))
            ], 201);

        } catch (\Exception $e) {
            // Log failed pointage
            Log::createLog(
                $userId,
                'pointage_attempt',
                'failed',
                [
                    'error' => $e->getMessage(),
                    'type' => $validated['type'] ?? 'unknown',
                    'site_id' => $validated['site_id'] ?? null
                ]
            );

            return response()->json([
                'success' => false,
                'message' => [
                    'en' => 'Failed to save pointage',
                    'fr' => 'Échec de l\'enregistrement du pointage',
                    'ar' => 'فشل في حفظ التوقيت'
                ],
                'error' => config('app.debug') ? $e->getMessage() : null
            ], 500);
        }
    }

    /**
     * Get pointages list (Admin only)
     */
    public function index(Request $request): JsonResponse
    {
        $query = Pointage::with(['user', 'site']);

        // Filter by date
        if ($request->has('date_from')) {
            $query->whereDate('debut_pointage', '>=', $request->date_from);
        }

        if ($request->has('date_to')) {
            $query->whereDate('debut_pointage', '<=', $request->date_to);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by site
        if ($request->has('site_id')) {
            $query->where('site_id', $request->site_id);
        }

        // Order by latest first
        $query->orderBy('debut_pointage', 'desc');

        // Paginate results
        $pointages = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => PointageResource::collection($pointages->items()),
            'pagination' => [
                'current_page' => $pointages->currentPage(),
                'last_page' => $pointages->lastPage(),
                'per_page' => $pointages->perPage(),
                'total' => $pointages->total(),
                'from' => $pointages->firstItem(),
                'to' => $pointages->lastItem(),
            ]
        ]);
    }
}
