<?php

/**
 * Test final simple de toutes les APIs
 */

echo "🎯 TEST FINAL SIMPLE API CLOCKIN\n";
echo "=================================\n\n";

$baseUrl = 'http://localhost:8001/api';
$tests = [];

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    $defaultHeaders = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $allHeaders = array_merge($defaultHeaders, $headers);
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $allHeaders,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        return ['success' => false, 'error' => $error, 'http_code' => 0];
    }
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'data' => json_decode($response, true),
        'raw_response' => $response
    ];
}

function test($name, $success) {
    global $tests;
    $status = $success ? '✅' : '❌';
    echo "{$status} {$name}\n";
    $tests[] = ['name' => $name, 'success' => $success];
    return $success;
}

// 1. Test login employé
$loginData = ['email' => '<EMAIL>', 'password' => 'password123'];
$response = makeRequest($baseUrl . '/login', 'POST', $loginData);
$loginSuccess = test('Login employé', $response['success'] && isset($response['data']['data']['token']));

if (!$loginSuccess) {
    echo "❌ Impossible de continuer sans login\n";
    exit(1);
}

$token = $response['data']['data']['token'];
$headers = ["Authorization: Bearer {$token}"];

// 2. Test profil utilisateur
$response = makeRequest($baseUrl . '/me', 'GET', null, $headers);
test('Profil utilisateur', $response['success']);

// 3. Test mes sites
$response = makeRequest($baseUrl . '/my-sites', 'GET', null, $headers);
test('Mes sites', $response['success']);

// 4. Test vérification localisation
$locationData = ['latitude' => 33.5731, 'longitude' => -7.5898, 'site_id' => 1];
$response = makeRequest($baseUrl . '/check-location', 'POST', $locationData, $headers);
test('Vérification localisation', $response['success']);

// 5. Test pointage (nouveau site pour éviter conflit)
$pointageData = ['site_id' => 2, 'type' => 'entree', 'latitude' => 33.5731, 'longitude' => -7.5898];
$response = makeRequest($baseUrl . '/save-pointage', 'POST', $pointageData, $headers);
test('Pointage entrée', $response['success']);

// 6. Test logout
$response = makeRequest($baseUrl . '/logout', 'POST', null, $headers);
test('Logout employé', $response['success']);

// 7. Test login admin
$adminData = ['email' => '<EMAIL>', 'password' => 'password123'];
$response = makeRequest($baseUrl . '/login', 'POST', $adminData);
$adminSuccess = test('Login admin', $response['success'] && isset($response['data']['data']['token']));

if ($adminSuccess) {
    $adminToken = $response['data']['data']['token'];
    $adminHeaders = ["Authorization: Bearer {$adminToken}"];
    
    // 8. Test endpoints admin
    $response = makeRequest($baseUrl . '/employees', 'GET', null, $adminHeaders);
    test('Liste employés (admin)', $response['success']);
    
    $response = makeRequest($baseUrl . '/sites', 'GET', null, $adminHeaders);
    test('Liste sites (admin)', $response['success']);
    
    $response = makeRequest($baseUrl . '/pointages', 'GET', null, $adminHeaders);
    test('Liste pointages (admin)', $response['success']);
    
    // 9. Logout admin
    $response = makeRequest($baseUrl . '/logout', 'POST', null, $adminHeaders);
    test('Logout admin', $response['success']);
}

// 10. Tests d'erreurs
$response = makeRequest($baseUrl . '/login', 'POST', ['email' => '<EMAIL>', 'password' => 'wrong']);
test('Login invalide (doit échouer)', !$response['success'] || $response['http_code'] === 401);

$response = makeRequest($baseUrl . '/me', 'GET');
test('Accès sans token (doit échouer)', !$response['success'] || $response['http_code'] === 401);

// Résumé
echo "\n" . str_repeat('=', 50) . "\n";
echo "📊 RÉSUMÉ FINAL\n";
echo str_repeat('=', 50) . "\n";

$passed = 0;
$total = count($tests);

foreach ($tests as $test) {
    $status = $test['success'] ? '✅' : '❌';
    echo "{$status} {$test['name']}\n";
    if ($test['success']) $passed++;
}

echo "\nRésultat: {$passed}/{$total} tests réussis\n";

if ($passed === $total) {
    echo "🎉 TOUTES LES APIS FONCTIONNENT PARFAITEMENT!\n";
    echo "✅ L'API ClockIn est entièrement opérationnelle\n\n";
    
    echo "📋 INFORMATIONS IMPORTANTES:\n";
    echo "• Serveur: http://localhost:8001\n";
    echo "• Login: POST /api/login\n";
    echo "• Endpoints protégés nécessitent: Authorization: Bearer {token}\n";
    echo "• Pointage: type 'entree' ou 'sortie'\n";
    echo "• Admin: accès complet aux données\n";
    echo "• Employé: accès limité à ses données\n";
} else {
    echo "⚠️  {$total - $passed} test(s) ont échoué\n";
}

exit($passed === $total ? 0 : 1);
