<?php

/**
 * Debug des endpoints employé
 */

echo "🔍 DEBUG ENDPOINTS EMPLOYÉ\n";
echo "===========================\n\n";

$baseUrl = 'http://localhost:8001/api';

function makeRequest($url, $method = 'GET', $data = null, $headers = []) {
    $ch = curl_init();
    
    $defaultHeaders = [
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $allHeaders = array_merge($defaultHeaders, $headers);
    
    curl_setopt_array($ch, [
        CURLOPT_URL => $url,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_HTTPHEADER => $allHeaders,
        CURLOPT_FOLLOWLOCATION => true
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    echo "📡 {$method} {$url}\n";
    if ($data) {
        echo "📤 Data: " . json_encode($data) . "\n";
    }
    echo "📊 HTTP Code: {$httpCode}\n";
    
    if ($error) {
        echo "❌ Error: {$error}\n";
        return ['success' => false, 'error' => $error, 'http_code' => 0];
    }
    
    $decodedResponse = json_decode($response, true);
    if ($decodedResponse) {
        echo "📥 Response: " . json_encode($decodedResponse, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
    } else {
        echo "📥 Raw Response: " . substr($response, 0, 200) . "...\n";
    }
    
    echo str_repeat('-', 50) . "\n";
    
    return [
        'success' => $httpCode >= 200 && $httpCode < 300,
        'http_code' => $httpCode,
        'data' => $decodedResponse,
        'raw_response' => $response
    ];
}

// 1. Login employé
echo "🔐 LOGIN EMPLOYÉ\n";
$loginData = [
    'email' => '<EMAIL>',
    'password' => 'password123'
];

$response = makeRequest($baseUrl . '/login', 'POST', $loginData);

if (!$response['success'] || !isset($response['data']['data']['token'])) {
    echo "❌ Impossible de se connecter\n";
    exit(1);
}

$token = $response['data']['data']['token'];
$headers = ["Authorization: Bearer {$token}"];

echo "✅ Token obtenu: " . substr($token, 0, 20) . "...\n\n";

// 2. Test /my-sites
echo "🏢 TEST /my-sites\n";
$response = makeRequest($baseUrl . '/my-sites', 'GET', null, $headers);

// 3. Test /check-location
echo "\n📍 TEST /check-location\n";
$locationData = [
    'latitude' => 33.5731,
    'longitude' => -7.5898,
    'site_id' => 1
];
$response = makeRequest($baseUrl . '/check-location', 'POST', $locationData, $headers);

// 4. Test /save-pointage
echo "\n⏰ TEST /save-pointage\n";
$pointageData = [
    'site_id' => 1,
    'type' => 'entree',
    'latitude' => 33.5731,
    'longitude' => -7.5898
];
$response = makeRequest($baseUrl . '/save-pointage', 'POST', $pointageData, $headers);

echo "\n✅ Debug terminé\n";
